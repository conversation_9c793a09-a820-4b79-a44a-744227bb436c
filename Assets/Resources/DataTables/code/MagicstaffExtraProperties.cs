
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using <PERSON><PERSON>;


namespace cfg
{
public sealed partial class MagicstaffExtraProperties : Luban.BeanBase
{
    public MagicstaffExtraProperties(ByteBuf _buf) 
    {
        Scattering = _buf.ReadFloat();
    }

    public static MagicstaffExtraProperties DeserializeMagicstaffExtraProperties(ByteBuf _buf)
    {
        return new MagicstaffExtraProperties(_buf);
    }

    /// <summary>
    /// 技能释放的范围额外增加的幅度
    /// </summary>
    public readonly float Scattering;
   
    public const int __ID__ = 636121968;
    public override int GetTypeId() => __ID__;

    public  void ResolveRef(Tables tables)
    {
    }

    public override string ToString()
    {
        return "{ "
        + "scattering:" + Scattering + ","
        + "}";
    }
}

}

