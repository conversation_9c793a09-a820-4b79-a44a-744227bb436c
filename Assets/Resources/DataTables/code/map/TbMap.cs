
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using <PERSON><PERSON>;


namespace cfg.map
{
public partial class TbMap
{
    private readonly System.Collections.Generic.Dictionary<int, map.MapConfiguration> _dataMap;
    private readonly System.Collections.Generic.List<map.MapConfiguration> _dataList;
    
    public TbMap(ByteBuf _buf)
    {
        _dataMap = new System.Collections.Generic.Dictionary<int, map.MapConfiguration>();
        _dataList = new System.Collections.Generic.List<map.MapConfiguration>();
        
        for(int n = _buf.ReadSize() ; n > 0 ; --n)
        {
            map.MapConfiguration _v;
            _v = map.MapConfiguration.DeserializeMapConfiguration(_buf);
            _dataList.Add(_v);
            _dataMap.Add(_v.Id, _v);
        }
    }

    public System.Collections.Generic.Dictionary<int, map.MapConfiguration> DataMap => _dataMap;
    public System.Collections.Generic.List<map.MapConfiguration> DataList => _dataList;

    public map.MapConfiguration GetOrDefault(int key) => _dataMap.TryGetValue(key, out var v) ? v : null;
    public map.MapConfiguration Get(int key) => _dataMap[key];
    public map.MapConfiguration this[int key] => _dataMap[key];

    public void ResolveRef(Tables tables)
    {
        foreach(var _v in _dataList)
        {
            _v.ResolveRef(tables);
        }
    }

}

}

