
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using <PERSON><PERSON>;


namespace cfg.item
{
public partial class TbItems
{
    private readonly System.Collections.Generic.Dictionary<int, item.Item> _dataMap;
    private readonly System.Collections.Generic.List<item.Item> _dataList;
    
    public TbItems(ByteBuf _buf)
    {
        _dataMap = new System.Collections.Generic.Dictionary<int, item.Item>();
        _dataList = new System.Collections.Generic.List<item.Item>();
        
        for(int n = _buf.ReadSize() ; n > 0 ; --n)
        {
            item.Item _v;
            _v = item.Item.DeserializeItem(_buf);
            _dataList.Add(_v);
            _dataMap.Add(_v.Id, _v);
        }
    }

    public System.Collections.Generic.Dictionary<int, item.Item> DataMap => _dataMap;
    public System.Collections.Generic.List<item.Item> DataList => _dataList;

    public item.Item GetOrDefault(int key) => _dataMap.TryGetValue(key, out var v) ? v : null;
    public item.Item Get(int key) => _dataMap[key];
    public item.Item this[int key] => _dataMap[key];

    public void ResolveRef(Tables tables)
    {
        foreach(var _v in _dataList)
        {
            _v.ResolveRef(tables);
        }
    }

}

}

