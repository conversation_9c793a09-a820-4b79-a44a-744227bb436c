
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using <PERSON><PERSON>;


namespace cfg
{
public sealed partial class RandomItem : Luban.BeanBase
{
    public RandomItem(ByteBuf _buf) 
    {
        ItemId = _buf.ReadInt();
        ItemCount = _buf.ReadInt();
    }

    public static RandomItem DeserializeRandomItem(ByteBuf _buf)
    {
        return new RandomItem(_buf);
    }

    /// <summary>
    /// 随机道具ID
    /// </summary>
    public readonly int ItemId;
    /// <summary>
    /// 随机道具数目
    /// </summary>
    public readonly int ItemCount;
   
    public const int __ID__ = 2108790422;
    public override int GetTypeId() => __ID__;

    public  void ResolveRef(Tables tables)
    {
    }

    public override string ToString()
    {
        return "{ "
        + "itemId:" + ItemId + ","
        + "itemCount:" + ItemCount + ","
        + "}";
    }
}

}

