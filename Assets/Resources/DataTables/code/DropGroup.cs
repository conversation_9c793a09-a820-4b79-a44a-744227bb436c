
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using <PERSON><PERSON>;


namespace cfg
{
public sealed partial class DropGroup : Luban.BeanBase
{
    public DropGroup(ByteBuf _buf) 
    {
        RandomGroupSetting = DropGroupSetting.DeserializeDropGroupSetting(_buf);
        {int n0 = System.Math.Min(_buf.ReadSize(), _buf.Size);Items = new System.Collections.Generic.List<DropGroupItem>(n0);for(var i0 = 0 ; i0 < n0 ; i0++) { DropGroupItem _e0;  _e0 = DropGroupItem.DeserializeDropGroupItem(_buf); Items.Add(_e0);}}
    }

    public static DropGroup DeserializeDropGroup(ByteBuf _buf)
    {
        return new DropGroup(_buf);
    }

    public readonly DropGroupSetting RandomGroupSetting;
    public readonly System.Collections.Generic.List<DropGroupItem> Items;
   
    public const int __ID__ = 1468265648;
    public override int GetTypeId() => __ID__;

    public  void ResolveRef(Tables tables)
    {
        RandomGroupSetting?.ResolveRef(tables);
        foreach (var _e in Items) { _e?.ResolveRef(tables); }
    }

    public override string ToString()
    {
        return "{ "
        + "randomGroupSetting:" + RandomGroupSetting + ","
        + "items:" + Luban.StringUtil.CollectionToString(Items) + ","
        + "}";
    }
}

}

