
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using <PERSON><PERSON>;


namespace cfg.skill
{
public sealed partial class Buff : Luban.BeanBase
{
    public Buff(ByteBuf _buf) 
    {
        Id = _buf.ReadInt();
        Name = _buf.ReadString();
        Desc = _buf.ReadString();
        Stype = (TypeOfBuff)_buf.ReadInt();
        Quality = (Quality)_buf.ReadInt();
        Target = (TargetOfBuff)_buf.ReadInt();
        EffectivePoint = (EffectivePoint)_buf.ReadInt();
        EmitNumber = _buf.ReadInt();
        EffectSize = _buf.ReadFloat();
    }

    public static Buff DeserializeBuff(ByteBuf _buf)
    {
        return new skill.Buff(_buf);
    }

    /// <summary>
    /// ID
    /// </summary>
    public readonly int Id;
    /// <summary>
    /// 名称
    /// </summary>
    public readonly string Name;
    /// <summary>
    /// 描述
    /// </summary>
    public readonly string Desc;
    /// <summary>
    /// 法术类型: 法术强化
    /// </summary>
    public readonly TypeOfBuff Stype;
    public readonly Quality Quality;
    public readonly TargetOfBuff Target;
    public readonly EffectivePoint EffectivePoint;
    /// <summary>
    /// 发射数量
    /// </summary>
    public readonly int EmitNumber;
    /// <summary>
    /// 尺寸
    /// </summary>
    public readonly float EffectSize;
   
    public const int __ID__ = 1560804848;
    public override int GetTypeId() => __ID__;

    public  void ResolveRef(Tables tables)
    {
    }

    public override string ToString()
    {
        return "{ "
        + "id:" + Id + ","
        + "name:" + Name + ","
        + "desc:" + Desc + ","
        + "stype:" + Stype + ","
        + "quality:" + Quality + ","
        + "target:" + Target + ","
        + "effectivePoint:" + EffectivePoint + ","
        + "emitNumber:" + EmitNumber + ","
        + "effectSize:" + EffectSize + ","
        + "}";
    }
}

}

