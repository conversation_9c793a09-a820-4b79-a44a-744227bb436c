
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using <PERSON><PERSON>;


namespace cfg.skill
{
public partial class TbBuff
{
    private readonly System.Collections.Generic.Dictionary<int, skill.Buff> _dataMap;
    private readonly System.Collections.Generic.List<skill.Buff> _dataList;
    
    public TbBuff(ByteBuf _buf)
    {
        _dataMap = new System.Collections.Generic.Dictionary<int, skill.Buff>();
        _dataList = new System.Collections.Generic.List<skill.Buff>();
        
        for(int n = _buf.ReadSize() ; n > 0 ; --n)
        {
            skill.Buff _v;
            _v = skill.Buff.DeserializeBuff(_buf);
            _dataList.Add(_v);
            _dataMap.Add(_v.Id, _v);
        }
    }

    public System.Collections.Generic.Dictionary<int, skill.Buff> DataMap => _dataMap;
    public System.Collections.Generic.List<skill.Buff> DataList => _dataList;

    public skill.Buff GetOrDefault(int key) => _dataMap.TryGetValue(key, out var v) ? v : null;
    public skill.Buff Get(int key) => _dataMap[key];
    public skill.Buff this[int key] => _dataMap[key];

    public void ResolveRef(Tables tables)
    {
        foreach(var _v in _dataList)
        {
            _v.ResolveRef(tables);
        }
    }

}

}

