
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using <PERSON><PERSON>;


namespace cfg
{
public sealed partial class RandomItemGroup : Luban.BeanBase
{
    public RandomItemGroup(ByteBuf _buf) 
    {
        Weight = _buf.ReadInt();
        {int n0 = System.Math.Min(_buf.ReadSize(), _buf.Size);Items = new System.Collections.Generic.List<RandomItem>(n0);for(var i0 = 0 ; i0 < n0 ; i0++) { RandomItem _e0;  _e0 = RandomItem.DeserializeRandomItem(_buf); Items.Add(_e0);}}
    }

    public static RandomItemGroup DeserializeRandomItemGroup(ByteBuf _buf)
    {
        return new RandomItemGroup(_buf);
    }

    /// <summary>
    /// 随机权重
    /// </summary>
    public readonly int Weight;
    /// <summary>
    /// 随机道具
    /// </summary>
    public readonly System.Collections.Generic.List<RandomItem> Items;
   
    public const int __ID__ = 1676746121;
    public override int GetTypeId() => __ID__;

    public  void ResolveRef(Tables tables)
    {
        foreach (var _e in Items) { _e?.ResolveRef(tables); }
    }

    public override string ToString()
    {
        return "{ "
        + "weight:" + Weight + ","
        + "items:" + Luban.StringUtil.CollectionToString(Items) + ","
        + "}";
    }
}

}

