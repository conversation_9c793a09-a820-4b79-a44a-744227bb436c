
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using <PERSON><PERSON>;


namespace cfg
{
public sealed partial class MonsterKey : Luban.BeanBase
{
    public MonsterKey(ByteBuf _buf) 
    {
        MonsterId = _buf.ReadInt();
        Location = _buf.ReadInt();
    }

    public static MonsterKey DeserializeMonsterKey(ByteBuf _buf)
    {
        return new MonsterKey(_buf);
    }

    /// <summary>
    /// 对应【怪物.xlsx】中的key
    /// </summary>
    public readonly int MonsterId;
    /// <summary>
    /// 把怪物放在地图上的位置
    /// </summary>
    public readonly int Location;
   
    public const int __ID__ = -173575099;
    public override int GetTypeId() => __ID__;

    public  void ResolveRef(Tables tables)
    {
    }

    public override string ToString()
    {
        return "{ "
        + "monsterId:" + MonsterId + ","
        + "location:" + Location + ","
        + "}";
    }
}

}

