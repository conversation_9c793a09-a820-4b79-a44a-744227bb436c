Shader "UI/Logo"
{
	Properties
	{
		_Color ("Tint", Color) = (1,1,1,1)
		_MainTex("MainTex", 2D) = "white" {}
		_Speed("Speed", Range(0, 1)) = 0
		_Smooth("Smooth", Range(0, 0.1)) = 0.03806899
		[Gamma] _Color0("Color 0", Color) = (0.8235294,0.854902,0.8235294,0)
	}
	
	SubShader
	{
		Tags { "RenderType"="Opaque" }
		Cull Off

		Pass
		{
			CGPROGRAM
			
			#pragma target 3.0 
			#pragma vertex vert
			#pragma fragment frag
			
			#include "UnityCG.cginc"
			
			struct appdata
			{
				float4 vertex : POSITION;
				float2 uv : TEXCOORD0;
	
				UNITY_VERTEX_INPUT_INSTANCE_ID
				
			};
			
			struct v2f
			{
				float4 vertex : SV_POSITION;
				float2 uv : TEXCOORD0;
				UNITY_VERTEX_OUTPUT_STEREO
				
			};

			uniform sampler2D _MainTex;
			uniform fixed4 _Color;
			uniform float4 _MainTex_ST;
			uniform float _Speed;
			uniform float _Smooth;
			uniform float4 _Color0;
			
			v2f vert ( appdata v )
			{
				v2f o;
				UNITY_SETUP_INSTANCE_ID(v);
				UNITY_INITIALIZE_VERTEX_OUTPUT_STEREO(o);
				o.uv = v.uv;
				o.vertex = UnityObjectToClipPos(v.vertex);
				return o;
			}
			
			fixed4 frag (v2f i ) : SV_Target
			{
				float2 uv = i.uv.xy * _MainTex_ST.xy + _MainTex_ST.zw;
				float4 maintex = tex2D(_MainTex, uv);
				float speed = clamp(frac(_Time.y * _Speed) * 1.5, 0.0, 1.0);
				float start = 1.0 - (speed + _Smooth);
				float alpha = smoothstep(start , start + _Smooth, maintex.r);
				
				fixed4 color = maintex.a * alpha * _Color0;
				
				return color;
			}
			ENDCG
		}
	}

}