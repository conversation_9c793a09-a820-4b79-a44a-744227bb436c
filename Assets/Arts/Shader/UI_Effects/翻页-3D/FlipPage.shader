Shader "Unlit/FlipPage"
{
    Properties
    {
        _MainTex ("Texture", 2D) = "white" {}
        _BackTex ("BackTex", 2D) = "white" {}
        _PageAngle ("Page Angle", Range(0,1)) = 0
        _CurveDegree ("Curve Degree", float) = 0
        [Toggle(SINGLE_PAGE)] _SinglePage ("Single Page", float) = 0
    }
    
    SubShader
    {
        Tags 
        { 
            "RenderType" = "Transparent" 
            "Queue" = "Transparent"
            "IgnoreProjector" = "True"
            "PreviewType" = "Plane"
        }
        
        CGINCLUDE
        #define PI 3.1415926
        #include "UnityCG.cginc"
        #pragma shader_feature __ SINGLE_PAGE

        struct appdata
        {
            float4 positionOS : POSITION;
            float2 uv : TEXCOORD0;
        };

        struct v2f
        {
            float2 uv : TEXCOORD0;
            float4 positionCS : SV_POSITION;
        };

        sampler2D _MainTex;
        float4 _MainTex_ST;

        sampler2D _BackTex;
        float4 _BackTex_ST;

        float _PageAngle;
        float _CurveDegree;

        float _SinglePage;

        float4 RotateAroundZDegrees(float4 vertex, float angle)
        {
            float a = angle * PI / 180.0;
            float cosa = cos(a);
            float sina = sin(a);
            float2x2 m = float2x2(cosa, -sina, sina, cosa);
            float4 rotate = float4(mul(m, vertex.xy), vertex.zw);

            return rotate;
        }

        v2f vert_flip (appdata v)
        {
            v2f o;
            
            o.uv = TRANSFORM_TEX(v.uv, _MainTex);
            o.uv.xy = 1 - o.uv.xy;

            float4 vertex = v.positionOS;
            float4 temp = vertex;

            float theta = _PageAngle * PI;

            float flip_curve = exp(-0.1 * pow(vertex.x , 2) + _CurveDegree)* _PageAngle;

            theta = lerp(theta - flip_curve, theta + flip_curve, _PageAngle * _SinglePage);

            temp.x = o.uv.x >= 0.5 ? vertex.x * cos(clamp(theta, 0, PI)) : -vertex.x * cos(clamp(theta, 0, PI));

            temp.y = vertex.x * sin(clamp(theta, 0, PI));

        #if SINGLE_PAGE
            v.positionOS = o.uv.x <= 0.5 ? v.positionOS : temp;
        #else
            v.positionOS = temp;
            v.positionOS = RotateAroundZDegrees(v.positionOS, 90);
        #endif
            
            o.positionCS = UnityObjectToClipPos(v.positionOS);
            
            return o;
        }
        
        /*
        v2f vert_next_page (appdata v)
        {
            v2f o;
            
            o.uv = TRANSFORM_TEX(v.uv, _MainTex);
            o.uv.y = 1 - o.uv.y;
            o.positionCS = UnityObjectToClipPos(v.positionOS);
  
            return o;
        }*/

        fixed4 frag_flip (v2f i) : SV_Target
        {
           
            fixed4 col = tex2D(_MainTex, i.uv);
            return col;
        }

        fixed4 frag_flip_back (v2f i) : SV_Target
        {
            i.uv.x = 1 - i.uv.x;
            fixed4 col = tex2D(_BackTex, i.uv);
            return col;
        }
        
        ENDCG
        
        //第一页
        Pass
        {
            Cull Back
            
            CGPROGRAM
            #pragma vertex vert_flip
            #pragma fragment frag_flip
            ENDCG
        }
        
        //背面
        Pass
        {
            Cull Front
            Offset -1, -1
            
            CGPROGRAM
            #pragma vertex vert_flip
            #pragma fragment frag_flip_back
            ENDCG
        }
    
        /*
        //背面
        Pass
        {
            Cull Front
            Offset -1, -1
            
            CGPROGRAM
            #pragma vertex vert_flip
            #pragma fragment frag_flip_back
            ENDCG
        }*/
    
    }
}
