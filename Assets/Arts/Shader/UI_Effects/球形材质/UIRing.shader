Shader "UI/Ring"
{
	Properties
	{
		[HDR]_Color("Color", Color) = (0,0,0,0)
		_Wide("Wide", Float) = 15.95
	}
	
	SubShader
	{
		Blend OneMinusDstColor One

		Pass
		{

			Tags 
			{ 
				"LightMode"="ForwardBase" 
				"RenderType"="Transparent"
			}
			
			CGPROGRAM

			#pragma target 3.0
			
			#pragma vertex vert
			#pragma fragment frag
			#pragma multi_compile_instancing
			
			#include "UnityCG.cginc"

			struct appdata
			{
				float4 vertex : POSITION;
				float4 color : COLOR;
				float2 uv : TEXCOORD0;
				UNITY_VERTEX_INPUT_INSTANCE_ID
			};
			
			struct v2f
			{
				float4 vertex : SV_POSITION;
				float2 uv : TEXCOORD1;
				UNITY_VERTEX_INPUT_INSTANCE_ID
				UNITY_VERTEX_OUTPUT_STEREO
			};

			uniform float4 _Color;
			uniform float _Wide;
			
			v2f vert ( appdata v )
			{
				v2f o;
				UNITY_SETUP_INSTANCE_ID(v);
				UNITY_INITIALIZE_VERTEX_OUTPUT_STEREO(o);
				UNITY_TRANSFER_INSTANCE_ID(v, o);

				o.uv = v.uv;
				o.vertex = UnityObjectToClipPos(v.vertex);
				
				return o;
			}
			
			fixed4 frag (v2f i ) : SV_Target
			{
				UNITY_SETUP_INSTANCE_ID(i);
				UNITY_SETUP_STEREO_EYE_INDEX_POST_VERTEX(i);
				
				float2 uv = i.uv + float2(-0.5, -0.5);
				float atan_uv = atan2(uv.x , uv.y);
				float progress = 1.0 - atan_uv;
				float mask = frac(atan_uv / UNITY_TWO_PI) * UNITY_TWO_PI - frac(_Time.y * 0.333) * UNITY_TWO_PI + 1.0;
				float distance = length(i.uv.xy * float2(1.5, 1.5) - float2(0.75, 0.75));
				
				float circle = 1.0 / _Wide / (_Wide * (1.0 - (min(
					1.0, max((step(0.0, progress) - step(1.0, progress) + step(1.0 , mask))
						* progress, mask)) * (1.0 - max(distance, 1.0 - distance)) + 0.5)));
				
				float smooth = smoothstep(0.77, 0.51, distance);
				
				fixed4 finalColor = float4(_Color.rgb * (circle * smooth), 0.0);
				
				return finalColor;
			}
			ENDCG
		}
	}

	
	
}
