// Made with Amplify Shader Editor
// Available at the Unity Asset Store - http://u3d.as/y3X 
Shader "penOut"
{
	Properties
	{
		_MainTex("MainTex", 2D) = "white" {}
		_Nus("Nus", Float) = 3
		_V("V", Range( -0.01 , 0.01)) = 0
		_U("U", Range( -0.01 , 0.01)) = 0
		_BGTex("BGTex", 2D) = "white" {}
		_Color("Color", Color) = (0,0,0,0)
		[HideInInspector] _texcoord( "", 2D ) = "white" {}
	}
	
	SubShader
	{
		Tags { "RenderType"="Opaque" }
		LOD 100
		CGINCLUDE
		#pragma target 3.0
		ENDCG
		Blend Off
		Cull Back
		ColorMask RGBA
		ZWrite On
		ZTest LEqual
		Offset 0 , 0
		
		

		Pass
		{
			Name "Unlit"
			CGPROGRAM
			#pragma vertex vert
			#pragma fragment frag
			#pragma multi_compile_instancing
			#include "UnityCG.cginc"
			

			struct appdata
			{
				float4 vertex : POSITION;
				UNITY_VERTEX_INPUT_INSTANCE_ID
				float4 ase_texcoord : TEXCOORD0;
			};
			
			struct v2f
			{
				float4 vertex : SV_POSITION;
				float4 ase_texcoord : TEXCOORD0;
				float4 ase_texcoord1 : TEXCOORD1;
				UNITY_VERTEX_OUTPUT_STEREO
				UNITY_VERTEX_INPUT_INSTANCE_ID
			};

			uniform float4 _Color;
			uniform sampler2D _BGTex;
			uniform float4 _BGTex_ST;
			uniform sampler2D _MainTex;
			uniform half _Nus;
			uniform float _U;
			uniform float _V;
			float forfor301( sampler2D Tex , int Nus , float2 one , float2 uv )
			{
				float outt = 0;
				for(int i = 0 ; i < Nus ; i ++){
				  float plus = i + 0.5;
				  float2 uvm = uv + (one * plus);
				  float2 uvn = uv - (one * plus);
				  outt += tex2D(Tex , uvm).y;
				  outt += tex2D(Tex , uvn).y;
				}
				return outt;
			}
			
			
			v2f vert ( appdata v )
			{
				v2f o;
				UNITY_SETUP_INSTANCE_ID(v);
				UNITY_INITIALIZE_VERTEX_OUTPUT_STEREO(o);
				UNITY_TRANSFER_INSTANCE_ID(v, o);

				float4 ase_clipPos = UnityObjectToClipPos(v.vertex);
				float4 screenPos = ComputeScreenPos(ase_clipPos);
				o.ase_texcoord1 = screenPos;
				
				o.ase_texcoord.xy = v.ase_texcoord.xy;
				
				//setting value to unused interpolator channels and avoid initialization warnings
				o.ase_texcoord.zw = 0;
				
				v.vertex.xyz +=  float3(0,0,0) ;
				o.vertex = UnityObjectToClipPos(v.vertex);
				return o;
			}
			
			fixed4 frag (v2f i ) : SV_Target
			{
				UNITY_SETUP_INSTANCE_ID(i);
				fixed4 finalColor;
				float2 uv_BGTex = i.ase_texcoord.xy * _BGTex_ST.xy + _BGTex_ST.zw;
				float4 tex2DNode302 = tex2D( _BGTex, uv_BGTex );
				sampler2D Tex301 = _MainTex;
				int Nus301 = (int)_Nus;
				float2 appendResult296 = (float2(_U , _V));
				float2 one301 = appendResult296;
				float4 screenPos = i.ase_texcoord1;
				float4 ase_screenPosNorm = screenPos/screenPos.w;
				ase_screenPosNorm.z = ( UNITY_NEAR_CLIP_VALUE >= 0 ) ? ase_screenPosNorm.z : ase_screenPosNorm.z * 0.5 + 0.5;
				float2 appendResult297 = (float2(ase_screenPosNorm.x , ase_screenPosNorm.y));
				float2 uv301 = appendResult297;
				float localforfor301 = forfor301( Tex301 , Nus301 , one301 , uv301 );
				float clampResult300 = clamp( localforfor301 , 0.0 , 1.0 );
				float4 lerpResult303 = lerp( _Color , tex2DNode302 , ( 1.0 - ( tex2DNode302.a * clampResult300 ) ));
				
				
				finalColor = lerpResult303;
				return finalColor;
			}
			ENDCG
		}
	}
	CustomEditor "ASEMaterialInspector"
	
	
}
/*ASEBEGIN
Version=16100
1;1;1847;1057;549.7031;146.1298;1;True;True
Node;AmplifyShaderEditor.ScreenPosInputsNode;293;-681.112,615.1428;Float;False;0;False;0;5;FLOAT4;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.RangedFloatNode;294;-700.4842,482.3285;Float;False;Property;_V;V;2;0;Create;True;0;0;False;0;0;0.001;-0.01;0.01;0;1;FLOAT;0
Node;AmplifyShaderEditor.RangedFloatNode;295;-749.8842,365.3286;Float;False;Property;_U;U;3;0;Create;True;0;0;False;0;0;-0.0006;-0.01;0.01;0;1;FLOAT;0
Node;AmplifyShaderEditor.DynamicAppendNode;296;-361.5198,446.9929;Float;False;FLOAT2;4;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.DynamicAppendNode;297;-438.3279,627.7431;Float;False;FLOAT2;4;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.TexturePropertyNode;298;-678.0243,5.22522;Float;True;Property;_MainTex;MainTex;0;0;Create;True;0;0;False;0;None;None;False;white;Auto;Texture2D;0;1;SAMPLER2D;0
Node;AmplifyShaderEditor.RangedFloatNode;299;-450.2861,259.9777;Half;False;Property;_Nus;Nus;1;0;Create;True;0;0;False;0;3;2;0;0;0;1;FLOAT;0
Node;AmplifyShaderEditor.CustomExpressionNode;301;68.88179,230.7349;Float;False;float outt = 0@$for(int i = 0 @ i < Nus @ i ++){$  float plus = i + 0.5@$  float2 uvm = uv + (one * plus)@$  float2 uvn = uv - (one * plus)@$  outt += tex2D(Tex , uvm).y@$  outt += tex2D(Tex , uvn).y@$}$return outt@;1;False;4;True;Tex;SAMPLER2D;;In;;Float;True;Nus;INT;0;In;;Float;True;one;FLOAT2;0,0;In;;Float;True;uv;FLOAT2;0,0;In;;Float;forfor;True;False;0;4;0;SAMPLER2D;;False;1;INT;0;False;2;FLOAT2;0,0;False;3;FLOAT2;0,0;False;1;FLOAT;0
Node;AmplifyShaderEditor.ClampOpNode;300;247.6928,227.3324;Float;False;3;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;1;False;1;FLOAT;0
Node;AmplifyShaderEditor.SamplerNode;302;83.83209,-6.447021;Float;True;Property;_BGTex;BGTex;4;0;Create;True;0;0;False;0;None;8c77b67cd241cd5488518d9daa23397e;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;6;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;306;417.0032,275.6362;Float;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.OneMinusNode;292;595.8323,296.2591;Float;False;1;0;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.ColorNode;305;663.8321,-63.44699;Float;False;Property;_Color;Color;5;0;Create;True;0;0;False;0;0,0,0,0;0.2569865,0.2614118,0.5188679,0;True;0;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.LerpOp;303;912.8321,214.553;Float;False;3;0;COLOR;0,0,0,0;False;1;COLOR;0,0,0,0;False;2;FLOAT;0;False;1;COLOR;0
Node;AmplifyShaderEditor.TemplateMultiPassMasterNode;8;1232.361,272.6356;Float;False;True;2;Float;ASEMaterialInspector;0;1;penOut;0770190933193b94aaa3065e307002fa;0;0;Unlit;2;True;0;1;False;-1;0;False;-1;0;1;False;-1;0;False;-1;True;0;False;-1;0;False;-1;True;0;False;-1;True;True;True;True;True;0;False;-1;True;False;255;False;-1;255;False;-1;255;False;-1;7;False;-1;1;False;-1;1;False;-1;1;False;-1;7;False;-1;1;False;-1;1;False;-1;1;False;-1;True;1;False;-1;True;3;False;-1;True;True;0;False;-1;0;False;-1;True;1;RenderType=Opaque=RenderType;True;2;0;False;False;False;False;False;False;False;False;False;False;0;;0;0;Standard;0;2;0;FLOAT4;0,0,0,0;False;1;FLOAT3;0,0,0;False;0
WireConnection;296;0;295;0
WireConnection;296;1;294;0
WireConnection;297;0;293;1
WireConnection;297;1;293;2
WireConnection;301;0;298;0
WireConnection;301;1;299;0
WireConnection;301;2;296;0
WireConnection;301;3;297;0
WireConnection;300;0;301;0
WireConnection;306;0;302;4
WireConnection;306;1;300;0
WireConnection;292;0;306;0
WireConnection;303;0;305;0
WireConnection;303;1;302;0
WireConnection;303;2;292;0
WireConnection;8;0;303;0
ASEEND*/
//CHKSM=30BCD0EEE0BCFD52715AD38E3B649A6CE332E095