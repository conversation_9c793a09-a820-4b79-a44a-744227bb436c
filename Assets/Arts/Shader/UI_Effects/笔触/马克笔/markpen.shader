// Made with Amplify Shader Editor
// Available at the Unity Asset Store - http://u3d.as/y3X 
Shader "markpen"
{
	Properties
	{
		_iMouse("iMouse", Vector) = (0,0,0,0)
		_MainTex("MainTex", 2D) = "white" {}
		[Toggle(_KEYWORD0_ON)] _Keyword0("Keyword 0", Float) = 0
		_Float2("Float 2", Float) = 0.9
	}
	
	SubShader
	{
		Tags { "RenderType"="Opaque" }
		LOD 100
		CGINCLUDE
		#pragma target 3.0
		ENDCG
		Blend Off
		Cull Back
		ColorMask RGBA
		ZWrite On
		ZTest LEqual
		Offset 0 , 0
		
		

		Pass
		{
			Name "Unlit"
			CGPROGRAM
			#pragma vertex vert
			#pragma fragment frag
			#pragma multi_compile_instancing
			#include "UnityCG.cginc"
			#include "UnityShaderVariables.cginc"
			#pragma shader_feature _KEYWORD0_ON


			struct appdata
			{
				float4 vertex : POSITION;
				UNITY_VERTEX_INPUT_INSTANCE_ID
				
			};
			
			struct v2f
			{
				float4 vertex : SV_POSITION;
				float4 ase_texcoord : TEXCOORD0;
				UNITY_VERTEX_OUTPUT_STEREO
				UNITY_VERTEX_INPUT_INSTANCE_ID
			};

			uniform float4 _iMouse;
			uniform sampler2D _MainTex;
			uniform float _Float2;
			float4 Draw1_g4( float2 fragCoord , float4 iMouse , float2 iResolution , sampler2D Tex , float bZ )
			{
				float4 fragColor;
				float brushSizee = bZ;
				float brushSize = (brushSizee*length(iResolution.xy)/256);
				if (fragCoord.x+fragCoord.y < 1) {
				  return fragColor = float4(iMouse.xy,1,0.);
				}
				float2 uv = fragCoord/iResolution.xy;
				float4 v = tex2D(Tex, uv);
				float2 currDelta = iMouse.xy-fragCoord;
				float len = length(currDelta)-brushSize;
				float linee;
				float brush;
				if (iMouse.z > 0) {
				    
				    float3 old = tex2D(Tex, float2(.5/iResolution.xy)).xyz;
				    //float3 old1 = tex2D(Tex, float2(.5/iResolution.xy) + float2(0.001,0.001)).xyz;
				    //float3 old2 = tex2D(Tex, float2(.5/iResolution.xy) - float2(0.001,0.001)).xyz;
				    //old += old1 + old2;
				    old.xy = lerp(iMouse.xy,old.xy,old.z);
				    float2 oldDelta = fragCoord-old.xy, newDelta = iMouse.xy-old.xy;
				    
				   linee =length(oldDelta-newDelta*clamp(dot(oldDelta,newDelta)/
					dot(newDelta,newDelta),0.,1.))-brushSize;
				    
				    float subLen = -1;//lerp(0.,length(oldDelta)-brushSize,old.z);
				    brush = clamp(-(linee-min(0.,subLen))/brushSize,0.,1.);
				    brush =max(brush , clamp(-(len-min(0.,subLen))/brushSize,0.,1.));
				}
				    v.xyz = v.xyz + step(0.01,brush);
				    v.xyz = clamp(v.xyz,0,1);
				fragColor = v;
				return fragColor;
			}
			
			
			v2f vert ( appdata v )
			{
				v2f o;
				UNITY_SETUP_INSTANCE_ID(v);
				UNITY_INITIALIZE_VERTEX_OUTPUT_STEREO(o);
				UNITY_TRANSFER_INSTANCE_ID(v, o);

				float4 ase_clipPos = UnityObjectToClipPos(v.vertex);
				float4 screenPos = ComputeScreenPos(ase_clipPos);
				o.ase_texcoord = screenPos;
				
				
				v.vertex.xyz +=  float3(0,0,0) ;
				o.vertex = UnityObjectToClipPos(v.vertex);
				return o;
			}
			
			fixed4 frag (v2f i ) : SV_Target
			{
				UNITY_SETUP_INSTANCE_ID(i);
				fixed4 finalColor;
				float4 screenPos = i.ase_texcoord;
				float4 ase_screenPosNorm = screenPos/screenPos.w;
				ase_screenPosNorm.z = ( UNITY_NEAR_CLIP_VALUE >= 0 ) ? ase_screenPosNorm.z : ase_screenPosNorm.z * 0.5 + 0.5;
				float2 appendResult2_g2 = (float2(ase_screenPosNorm.x , ase_screenPosNorm.y));
				float2 appendResult3_g2 = (float2(_ScreenParams.x , _ScreenParams.y));
				float2 fragCoord1_g4 = ( appendResult2_g2 * appendResult3_g2 );
				float4 iMouse1_g4 = _iMouse;
				float2 appendResult196 = (float2(_ScreenParams.x , _ScreenParams.y));
				float2 iResolution1_g4 = appendResult196;
				sampler2D Tex1_g4 = _MainTex;
				float bZ1_g4 = _Float2;
				float4 localDraw1_g4 = Draw1_g4( fragCoord1_g4 , iMouse1_g4 , iResolution1_g4 , Tex1_g4 , bZ1_g4 );
				float3 temp_output_265_0 = (localDraw1_g4).xyz;
				float2 appendResult222 = (float2(ase_screenPosNorm.x , ase_screenPosNorm.y));
				float3 temp_cast_0 = (tex2D( _MainTex, appendResult222 ).b).xxx;
				#ifdef _KEYWORD0_ON
				float3 staticSwitch215 = max( temp_cast_0 , temp_output_265_0 );
				#else
				float3 staticSwitch215 = ( float3( 0,0,0 ) * temp_output_265_0 );
				#endif
				
				
				finalColor = float4( staticSwitch215 , 0.0 );
				return finalColor;
			}
			ENDCG
		}
	}
	CustomEditor "ASEMaterialInspector"
	
	
}
/*ASEBEGIN
Version=16100
1;1;1856;1057;-476.1845;656.042;1.3;True;True
Node;AmplifyShaderEditor.ScreenParams;195;1334.938,-0.3565979;Float;False;0;5;FLOAT4;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.Vector4Node;194;839.9996,-138.0095;Float;False;Property;_iMouse;iMouse;0;0;Create;True;0;0;False;0;0,0,0,0;1619.501,550.7888,-1,-1.210611;0;5;FLOAT4;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.TexturePropertyNode;198;1672.738,311.1433;Float;True;Property;_MainTex;MainTex;1;0;Create;True;0;0;False;0;None;None;False;white;Auto;Texture2D;0;1;SAMPLER2D;0
Node;AmplifyShaderEditor.FunctionNode;293;1595.938,-167.3566;Float;False;ScreenUV;-1;;2;e49fe2f436abc9d43bf8068a1dc44841;0;0;1;FLOAT2;0
Node;AmplifyShaderEditor.DynamicAppendNode;196;1595.938,40.6434;Float;False;FLOAT2;4;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.RangedFloatNode;286;1327.89,299.0641;Float;False;Property;_Float2;Float 2;3;0;Create;True;0;0;False;0;0.9;0.74;0;0;0;1;FLOAT;0
Node;AmplifyShaderEditor.ScreenPosInputsNode;219;1518.656,511.9247;Float;False;0;False;0;5;FLOAT4;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.FunctionNode;410;1962.649,-20.80771;Float;False;markpenDraw;-1;;4;8017a428ca1681e4ca88310abfce5aaa;0;5;2;FLOAT2;0,0;False;3;FLOAT4;0,0,0,0;False;4;FLOAT2;0,0;False;6;FLOAT;0;False;5;SAMPLER2D;0;False;1;FLOAT4;0
Node;AmplifyShaderEditor.DynamicAppendNode;222;1836.84,512.825;Float;False;FLOAT2;4;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT2;0
Node;AmplifyShaderEditor.ComponentMaskNode;265;2295.467,-18.4825;Float;False;True;True;True;False;1;0;FLOAT4;0,0,0,0;False;1;FLOAT3;0
Node;AmplifyShaderEditor.SamplerNode;249;2070.497,351.8652;Float;True;Property;_TextureSample0;Texture Sample 0;1;0;Create;True;0;0;False;0;37a5ff65fbc02af4caf2d02a6eed2778;37a5ff65fbc02af4caf2d02a6eed2778;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;6;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;214;2562.396,10.5791;Float;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0
Node;AmplifyShaderEditor.SimpleMaxOpNode;258;2558.671,190.8586;Float;False;2;0;FLOAT;0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;361;982.444,189.787;Float;False;2;2;0;FLOAT;0;False;1;FLOAT;3;False;1;FLOAT;0
Node;AmplifyShaderEditor.ClampOpNode;360;1154.444,198.787;Float;False;3;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;1;False;1;FLOAT;0
Node;AmplifyShaderEditor.StaticSwitch;215;2767.303,104.9051;Float;False;Property;_Keyword0;Keyword 0;2;0;Create;True;0;0;False;0;0;0;1;True;;Toggle;2;Key0;Key1;9;1;FLOAT3;0,0,0;False;0;FLOAT3;0,0,0;False;2;FLOAT3;0,0,0;False;3;FLOAT3;0,0,0;False;4;FLOAT3;0,0,0;False;5;FLOAT3;0,0,0;False;6;FLOAT3;0,0,0;False;7;FLOAT3;0,0,0;False;8;FLOAT3;0,0,0;False;1;FLOAT3;0
Node;AmplifyShaderEditor.TemplateMultiPassMasterNode;8;3305.755,91.32899;Float;False;True;2;Float;ASEMaterialInspector;0;1;markpen;0770190933193b94aaa3065e307002fa;0;0;Unlit;2;True;0;1;False;-1;0;False;-1;0;1;False;-1;0;False;-1;True;0;False;-1;0;False;-1;True;0;False;-1;True;True;True;True;True;0;False;-1;True;False;255;False;-1;255;False;-1;255;False;-1;7;False;-1;1;False;-1;1;False;-1;1;False;-1;7;False;-1;1;False;-1;1;False;-1;1;False;-1;True;1;False;-1;True;3;False;-1;True;True;0;False;-1;0;False;-1;True;1;RenderType=Opaque=RenderType;True;2;0;False;False;False;False;False;False;False;False;False;False;0;;0;0;Standard;0;2;0;FLOAT4;0,0,0,0;False;1;FLOAT3;0,0,0;False;0
WireConnection;196;0;195;1
WireConnection;196;1;195;2
WireConnection;410;2;293;0
WireConnection;410;3;194;0
WireConnection;410;4;196;0
WireConnection;410;6;286;0
WireConnection;410;5;198;0
WireConnection;222;0;219;1
WireConnection;222;1;219;2
WireConnection;265;0;410;0
WireConnection;249;0;198;0
WireConnection;249;1;222;0
WireConnection;214;1;265;0
WireConnection;258;0;249;3
WireConnection;258;1;265;0
WireConnection;361;0;194;4
WireConnection;360;0;361;0
WireConnection;215;1;214;0
WireConnection;215;0;258;0
WireConnection;8;0;215;0
ASEEND*/
//CHKSM=C870F7B62A4E1FCDD7516AF3DCDA58BBCA9E42DE