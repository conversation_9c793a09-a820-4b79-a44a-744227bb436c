using UnityEngine;

[ExecuteInEditMode]
public class PostProcess : MonoBehaviour
{
	public Material RToneProcessMat;
	//public Material RTtowProcessMat;
	public Material LastPostProcessMat;
	public RenderTexture m_renderTex01;
	public RenderTexture m_renderTex02;
	//public RenderTexture m_renderTex03;
	//public RenderTexture m_renderTex04;
	private bool _isDragging = true;
	float m_fTouchValue = 0.0f;
	float i_fTouchValue = 0.0f;
	public float m_fTouchSpeed = 1.0f;
	void Start () {
        Renderer render  = GetComponent<Renderer>();
        if (render != null) {
        }

        _isDragging = false;
    }


	private void Awake()
	{
		if( RToneProcessMat == null )
		{
			enabled = false;
		}

	}

	void OnRenderImage( RenderTexture src, RenderTexture dest )
	{

		Graphics.Blit(m_renderTex01 ,m_renderTex02 ,RToneProcessMat );
		Graphics.Blit(m_renderTex02 , m_renderTex01 );

		//Graphics.Blit(m_renderTex03 ,m_renderTex04 ,RTtowProcessMat );
		//Graphics.Blit(m_renderTex04 , m_renderTex03 );


		Graphics.Blit(m_renderTex01, dest , LastPostProcessMat );
	}



	void Update () {

	Vector4 mousePosition = Vector4.zero;

	if (Input.GetMouseButton(0) )
     {
		if( false == _isDragging )
		 	m_fTouchValue = 0.0f;
		 	//i_fTouchValue = 0.0f;

        _isDragging = true;
     }
     else{
		 
		 if( true == _isDragging )
		 	m_fTouchValue = 0.0f;

     	_isDragging = false;

	 }

    if (_isDragging == true) {
		m_fTouchValue += Time.deltaTime*m_fTouchSpeed;
		i_fTouchValue = Mathf.Clamp(m_fTouchValue , 0 , 1);
	    mousePosition = new Vector4(Input.mousePosition.x, Input.mousePosition.y, 1 ,i_fTouchValue);
	} else {
		m_fTouchValue -= Time.deltaTime*m_fTouchSpeed;
		i_fTouchValue -= Time.deltaTime*m_fTouchSpeed;
	    mousePosition = new Vector4(Input.mousePosition.x, Input.mousePosition.y, -1 ,i_fTouchValue);
	}

	if (RToneProcessMat != null) {
	    RToneProcessMat.SetVector("_iMouse", mousePosition);
	}
//
	//if (RTtowProcessMat != null) {
	//    RTtowProcessMat.SetVector("_sMouse", mousePosition);
	//}
//
	if (LastPostProcessMat != null) {
	    LastPostProcessMat.SetVector("_pMouse", mousePosition);
	}
	
    }


}
