Shader "Unlit/Page"
{
    Properties
    {
        _MainTex ("Front Tex", 2D) = "white" {}
        _BackTex ("Back Tex", 2D) = "white" {}
        _MaskTex ("Mask Tex", 2D) = "white" {}
        _Progress ("Progress", float) = 0
        _Radius ("Radius", Range(0,1)) = 0.1
        _DirX ("Direction X", float) = 0
        _DirY ("Direction Y", float) = 0
        _ShadowColor ("Shadow Color", color) = (0,0,0,0)
        _ShadowRange ("Shadow Range(XY ZW)", vector) = (0.3, 2.0, 0, 1.5)
        
        [Space]
        [Toggle(DOWN_TO_UP)] _DownToUp ("从下至上卷起", float) = 0
        [Space(20)]
        
        _StencilComp ("Stencil Comparison", float) = 8
        _Stencil ("Stencil ID", float) = 0
        _StencilOp ("Stencil Operation", float) = 0
        _StencilReadMask ("Stencil Read Mask", float) = 255
        _StencilWriteMask ("Stencil Write Mask", float) = 255
        _ColorMask ("Color Mask", float) = 15
    }
    
    SubShader
    {
        Tags 
        { 
            "RenderType" = "Transparent" 
            "Queue" = "Transparent"
            "IgnoreProjector" = "True"
            "PreviewType" = "Plane"
        }
        
        Stencil
        {
            Ref [_Stencil]
            Comp [_StencilComp]
            Pass [_StencilOp]
            ReadMask [_StencilReadMask]
            WriteMask [_StencilWriteMask]
        }
        
        Cull Off
        Lighting Off
        Zwrite Off
        Blend SrcAlpha OneMinusSrcAlpha

        Pass
        {
            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #include "UnityCG.cginc"

            #pragma shader_feature_local __ DOWN_TO_UP
            struct appdata
            {
                float4 vertex : POSITION;
                float2 uv : TEXCOORD0;
                float4 color : COLOR;
            };

            struct v2f
            {
                float2 uv : TEXCOORD0;
                float4 vertex : SV_POSITION;
                float4 color : TEXCOORD1;
            };

            sampler2D _MainTex;
            float4 _MainTex_ST;
            sampler2D _BackTex;
            float4 _BackTex_ST;
            sampler2D _MaskTex;
            float4 _MaskTex_ST;
            fixed _Progress;
            fixed _Radius;
            fixed _DirX;
            fixed _DirY;
            fixed4 _ShadowColor;
            fixed4 _ShadowRange;

            v2f vert (appdata v)
            {
                v2f o;
                o.vertex = UnityObjectToClipPos(v.vertex);
                o.uv = TRANSFORM_TEX(v.uv, _MainTex);
                o.color = v.color;
                return o;
            }

            fixed4 frag (v2f i) : SV_Target
            {

                fixed2 uv = i.uv;
                
            #if DOWN_TO_UP
                uv = half2(1 - i.uv.y, i.uv.x);
            #endif
                
                //卷页方向
                fixed2 direction = fixed2(_DirX, _DirY) * 0.1;
                fixed radius = _Radius * 0.1;
                fixed t = distance(mul(clamp(radius, 0.0, 1.0) * 4.0 + 1.0, direction), radius * 2.0);
                
                fixed dir = dot(uv + _Progress, normalize(direction));
                fixed2 divide = (1 - uv) / normalize(direction);
                fixed curve_divi = min(divide.x, divide.y) + dir;
                
                if(_DirY <= 0)
                {
                    dir = 1 - dot(1 - uv - _Progress, normalize(direction));
                    divide = abs( fixed2(1 - uv.x, uv.y) / normalize(direction));
                    curve_divi = min(divide.x, divide.y) + dir;
                }

                fixed shadow = 0.0;
                fixed2 gv = fixed2(0.0, 0.0);
                fixed isback = 0.0;

                //卷页正面
                if(dir < t - radius)
                {
                    gv = fixed2(dir, 1.0);
                }
                //卷页后面
                else if(dir > t + radius)
                {
                    gv = fixed2(-1.0, -1.0);
                }
                //卷起部分
                else
                {
                    fixed eage = asin((dir - t) / radius);
                    fixed ceage = -eage + 3.141592;
                    gv.x = t + ceage * radius;
                    gv.y = cos(ceage);

                    //卷页前边阴影
                    if(gv.x < curve_divi)
                    {
                        isback = 1.0;
                        shadow = clamp(0.0, 1.0, smoothstep(_ShadowRange.x, _ShadowRange.y, abs(eage)));
                    }

                    if(gv.x >= curve_divi)
                    {
                        if(dir < t)
                        {
                            gv = fixed2(dir, 1.0);
                        }
                        else
                        {
                            gv.y = cos(eage);
                            gv.x = t + eage * radius;

                            shadow = clamp(0.0, 1.0, smoothstep(_ShadowRange.z, _ShadowRange.w, 1.0 - gv.y));

                            if(gv.x >= curve_divi)
                            {
                                gv = fixed2(-1.0, -1.0);
                            }
                        }
                    }
                }

                fixed front_curve = clamp(max(gv.x, gv.y) * 2.0, 0.0, 1.0);
                fixed2 front_uv = front_curve * uv;
                fixed2 curve_uv = front_uv + (gv.x - dir) * normalize(direction);

                fixed2 front_curve_uv = front_uv + (gv.x - dir) * normalize(direction) * (1 - isback);
                fixed4 front_curve_col = tex2D(_MainTex, front_curve_uv);
                fixed4 back_curve_col = tex2D(_BackTex, curve_uv);

                fixed mask = tex2D(_MaskTex, curve_uv).r;
                fixed3 finalcol = lerp(front_curve_col.rgb, back_curve_col.rgb, isback * mask);

                shadow *= mask;

                mask = front_curve * max(front_curve_col.a, back_curve_col.a) * i.color.a;

                finalcol = lerp(finalcol, _ShadowColor.rgb * mask, shadow) * i.color.rgb;
                
                return half4(finalcol, mask);
            }
            ENDCG
        }
    }
}
