using UnityEngine;
using UnityEditor;

namespace Bioum
{
    public class ColorShiftDrawer : MaterialPropertyDrawer
    {
        public override void OnGUI(Rect position, MaterialProperty prop, string label, MaterialEditor editor)
        {
            var oldCol = prop.colorValue;
            var newCol = EditorGUI.ColorField(position, label, prop.colorValue);

            // 不用 change check 的原因
            // 多材质编辑时， 如果没有数据，会走change 流程
            // 出现这种情况， 所有颜色都会变成黑色
            if (newCol != oldCol)
            {
                prop.colorValue = newCol;

                Vector3 hsv;
                Color.RGBToHSV(prop.colorValue, out hsv.x, out hsv.y, out hsv.z);
                var hsvPropName = prop.name.Replace("RGB", "HSV");
                foreach (var target in prop.targets)
                {
                    var mat = target as Material;
                    mat.SetVector(hsvPropName, hsv);
                }
            }
        }
    }
}
    
  




