using System;

public enum CharacterType
{
    <PERSON>,
    Monster
}

public class MonsterKey
{
    public int ID { get; }
    public int LocationID { get; }

    public MonsterKey(int id, int locationID)
    {
        ID = id;
        LocationID = locationID;
    }

    // 重写 Equals 和 GetHashCode 方法
    public override bool Equals(object obj)
    {
        if (obj is MonsterKey other)
        {
            return ID == other.ID && LocationID == other.LocationID;
        }
        return false;
    }

    public override int GetHashCode()
    {
        return HashCode.Combine(ID, LocationID);
    }
}