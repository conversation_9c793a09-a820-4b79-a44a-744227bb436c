using MoreMountains.Tools;

namespace TopDownEngine.Scripts.Characters.AI.Util
{
    /// <summary>
    /// 轮流进入true和false状态
    /// </summary>
    public class AIDecisionZigZagState : AIDecision
    {
        private int stateNum = 2;
        private int last_release_index = -1;

        public override bool Decide()
        {
            last_release_index++;
            return last_release_index % stateNum == 0;
        }
    }
}