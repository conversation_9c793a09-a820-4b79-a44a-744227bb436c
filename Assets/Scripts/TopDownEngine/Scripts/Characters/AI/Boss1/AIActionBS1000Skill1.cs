using System;
using MoreMountains.Tools;
using MoreMountains.TopDownEngine;
using UnityEngine;

namespace TopDownEngine.Scripts.Characters.AI
{
    public class AIActionBS1000Skill1 : AIAction
    {
        public Transform parent;
        public MMSimpleObjectPooler simplePool;
        public Animator bossAnimator;
        public float totalTime = 0.5f;
        public float intervalAngle = 30;
        
        private float skillTime;
        private float intervalTime;
        private float curAngle = 0;
        private float lastReleaseTime = 0;
        private float releaseOffset = 2;

        public override void OnEnterState()
        {
            curAngle = 0;
            lastReleaseTime = 0f;
            intervalTime = totalTime / (360 / intervalAngle);
            bossAnimator.SetLayerWeight(1, 1);
            bossAnimator.Play("skill1", 1, 0);
        }

        public override void OnExitState()
        {
            bossAnimator.SetLayerWeight(1, 0);
        }

        public override void PerformAction()
        {
            if (lastReleaseTime + intervalTime < Time.time && curAngle < 360)
            {
                lastReleaseTime = Time.time;
                GameObject nextGameObject = simplePool.GetPooledGameObject();
                if (nextGameObject == null)
                {
                    return;
                }
                var poolObj = nextGameObject.GetComponent<MMPoolableObject>();
                if (poolObj == null)
                {
                    throw new Exception(gameObject.name + " is trying to spawn objects that don't have a PoolableObject component.");
                }
                
                var projectTile = nextGameObject.GetComponent<Projectile>();
                
                var direction = (Quaternion.Euler(0, 0, curAngle) * -gameObject.transform.up).normalized;
                nextGameObject.transform.position = parent.position + releaseOffset * direction;
                nextGameObject.transform.right = direction;
                projectTile.SetDirection(direction, nextGameObject.transform.rotation);
                curAngle += intervalAngle;
                poolObj.TriggerOnSpawnComplete();
                nextGameObject.SetActive(true);
            }
        }
    }
}