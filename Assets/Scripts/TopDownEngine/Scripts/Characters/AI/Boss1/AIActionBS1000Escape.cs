using System;
using cfg;
using MoreMountains.Tools;
using MoreMountains.TopDownEngine;
using Unity.Mathematics;
using UnityEngine;
using UnityEngine.Serialization;

namespace TopDownEngine.Scripts.Characters.AI.Boss1
{
    public class AIActionBS1000Escape : AIAction
    {

        public Collider2D hitCollider2D;
        public Transform parent;
        public Animator bossAnimator;
        public float upAnimTime = 2.5f;
        public float upAnimSpeed = 2f;
        public float moveToTargetTime = 1.5f;
        public float waitDownTime = 0.2f;
        
        private float startTime = 0;
        private bool _haveBeginMove = false;
        private bool _haveDown = false;
        private float _curMoveToTargetTime = 0;
        private Vector3 _beginMoveToTargetPosition = new Vector3();

        private GameObject target = null;
        public override void OnEnterState()
        {
            _haveBeginMove = false;
            _haveDown = false;
            startTime = Time.time;
            bossAnimator.SetLayerWeight(1, 1);
            bossAnimator.Play("skill2_up", 1, 0);
            bossAnimator.speed = upAnimSpeed;

            hitCollider2D.enabled = false;
        }

        
        public override void PerformAction()
        {
            _curMoveToTargetTime += Time.deltaTime;
            if (!_haveDown && !_haveBeginMove && startTime + upAnimTime / upAnimSpeed <= Time.time)
            {
                _haveBeginMove = true;
                //时间插值保证一定能到玩家位置
                _beginMoveToTargetPosition = parent.position;
                _curMoveToTargetTime = 0;
            }

            if (_haveBeginMove && _curMoveToTargetTime <= moveToTargetTime)
            {
                if (target == null)
                {
                    target = ProgrammableWeaponCommonFunctions.FindNearestCharacter(transform.position, tag: "Player");
                }
                //todo后面导个曲线工具可配置移动速度变化
                parent.position = Vector3.Lerp(_beginMoveToTargetPosition, target.transform.position, _curMoveToTargetTime / moveToTargetTime);
            }

            if (_haveBeginMove && _curMoveToTargetTime >= moveToTargetTime + waitDownTime)
            {
                target = null;
                _haveBeginMove = false;
                _haveDown = true;
                bossAnimator.Play("skill2_down", 1, 0);
            }
        }

        public override void OnExitState()
        {
            bossAnimator.SetLayerWeight(1, 0);
            hitCollider2D.enabled = true;
        }
    }
}