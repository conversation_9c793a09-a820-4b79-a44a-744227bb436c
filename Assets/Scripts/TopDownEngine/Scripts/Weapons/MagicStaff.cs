// 法杖基类
using System.Collections.Generic;
using System;
using System.Reflection;

public class MagicStaff
{
    public uint Id { get; set; } // 法杖ID
    public string Name { get; set; } // 法杖名称
    public float MaxMagicValue { get; set; } // 最大魔法值
    public float MinMagicValue { get; set; } // 最小魔法值
    public float MagicRegen { get; set; } // 回魔
    public float MagicConsume { get; set; } // 消耗魔法值
    public float Cooldown { get; set; } // 冷却
    public float CastInterval { get; set; } // 施法间隔

    public float Scattering { get; set; } // 散射

    public uint SlotNumber { get; set; } // 槽位数目

    public float Range { get; set; } // 射程

    public float CurrentMagicValue { get; set; } // 当前魔法值

    public Dictionary<string, string> BaseProperties = new Dictionary<string, string>
    {
        { "MaxMagicValue", "魔法值" },
        { "MagicRegen", "回魔" },
        { "MagicConsume", "魔耗" },
        { "Cooldown", "冷却" },
        { "CastInterval", "施法间隔" }
    };

    public Dictionary<string, string> ExtraProperties = new Dictionary<string, string>
    {
        { "Scattering", "散射" },
    };

    public MagicStaff(uint id, string name, float magicValue, float magicRegen, float magicConsume, float cooldown, float castInterval, uint slotNumber, float scattering)
    {
        Id = id;
        Name = name;
        MaxMagicValue = magicValue;
        MagicRegen = magicRegen;
        Cooldown = cooldown;
        CastInterval = castInterval;
        SlotNumber = slotNumber;
        CurrentMagicValue = MaxMagicValue;
        MagicConsume = magicConsume;
        MinMagicValue = 0f;
        Scattering = scattering;
    }

    public MagicStaff(cfg.item.Weapon weapon)
    {
        Id = (uint)weapon.Id;
        Name = weapon.Name;
        MaxMagicValue = weapon.BaseProperties.MaxMagicValue;
        MagicRegen = weapon.BaseProperties.MagicRegen;
        Cooldown = weapon.BaseProperties.Cooldown;
        CastInterval = weapon.BaseProperties.CastInterval;
        SlotNumber = (uint)weapon.SlotNum;
        CurrentMagicValue = MaxMagicValue;
        MagicConsume = weapon.BaseProperties.MagicConsume;
        MinMagicValue = 0f;
        Scattering = weapon.ExtraProperties.Scattering;
        Range = weapon.Range;
    }

    public static bool IsNumeric(object value)
    {
        if (value == null) return false;

        try
        {
            Convert.ToDouble(value);
            return true;
        }
        catch
        {
            return false;
        }
    }

    // Function to get all base properties with their types
    public Dictionary<string, (object Value, Type Type)> GetBasePropertiesWithTypes()
    {
        var propertiesWithTypes = new Dictionary<string, (object, Type)>();

        foreach (var property in typeof(MagicStaff).GetProperties(BindingFlags.Public | BindingFlags.Instance))
        {
            if (BaseProperties.ContainsKey(property.Name))
            {
                var value = property.GetValue(this);
                if (!IsNumeric(value) || Convert.ToDouble(value) <= 0f)
                { // 若属性值不是数字或小于等于0，则说明无此属性
                    continue;
                }
                var type = property.PropertyType;
                propertiesWithTypes.Add(property.Name, (value, type));
            }
        }

        return propertiesWithTypes;
    }

    // Function to get all extended properties with their types
    public Dictionary<string, (object Value, Type Type)> GetExtendedPropertiesWithTypes()
    {
        var propertiesWithTypes = new Dictionary<string, (object, Type)>();

        foreach (var property in typeof(MagicStaff).GetProperties(BindingFlags.Public | BindingFlags.Instance))
        {
            if (ExtraProperties.ContainsKey(property.Name))
            {
                var value = property.GetValue(this);
                if (!IsNumeric(value) || Convert.ToDouble(value) <= 0f)
                {
                    continue;
                }
                var type = property.PropertyType;
                propertiesWithTypes.Add(property.Name, (value, type));
            }
        }

        return propertiesWithTypes;
    }

    // Test function to demonstrate type conversion
    public void TestPropertyConversion()
    {
        var baseProperties = GetBasePropertiesWithTypes();

        foreach (var property in baseProperties)
        {
            Console.WriteLine($"Property: {property.Key}, Value: {property.Value.Value}, Type: {property.Value.Type}");

            // Example of converting the property value to its type
            if (property.Value.Type == typeof(float))
            {
                float floatValue = Convert.ToSingle(property.Value.Value);
                Console.WriteLine($"Converted {property.Key} to float: {floatValue}");
            }
            // Add more type checks and conversions as needed
        }
    }
}
