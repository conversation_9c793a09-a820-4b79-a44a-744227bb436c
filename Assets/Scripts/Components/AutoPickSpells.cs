using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class AutoPickSpells : BaseDropItem
{
    private ElementType m_spellType = 0;
    private int m_spellID = 0;

    void Start()
    {
    }

    void OnTriggerEnter2D(Collider2D other)
    {
        if (other.CompareTag("Player"))
        {
            // 获取物品ID
            ItemID = GetComponent<BaseDropItem>().ItemID;
            // 获取法术类型
            int type = DataTableManager.Instance.Tables.TbItems.Get(ItemID).Type;
            if (type == 2)
            {
                m_spellType = ElementType.Skill;
            }
            else if (type == 3)
            {
                m_spellType = ElementType.Buff;
            }
            // 获取法术ID
            List<int> spellIDs = DataTableManager.Instance.Tables.TbItems.Get(ItemID).SubItem;
            m_spellID = spellIDs[0];

            if (m_spellType == ElementType.Skill) 
            {
                SkillElement se = null;
                // 目前技能只有一种， 暂时这样写
                if (m_spellID == 10001)
                {
                    se = new SkillProjectile((uint)m_spellID, "法术: 飞弹");
                }else if (m_spellID == 10002)
                {
                    se = new SkillBlackHole((uint)m_spellID, "法术：黑洞");
                }

                se.SlotType = 2;
                PlayerData.GetInstance().AddSlotElement(se);
            }
            else
            {
                // Buff
                cfg.skill.Buff buff = DataTableManager.Instance.Tables.TbBuff.Get(m_spellID);

                // 使用 BuffFactory 创建对应类型的 Buff
                var buffElement = BuffFactory.CreateBuff(buff);

                if (buffElement != null)
                {
                    buffElement.SlotType = 2;
                    PlayerData.GetInstance().AddSlotElement(buffElement);
                }
                else
                {
                    Debug.LogWarning($"未找到ID为 {m_spellID} 的Buff创建方法");
                }
            }

            // 销毁物品
            Destroy(gameObject);
        }
    }
}
