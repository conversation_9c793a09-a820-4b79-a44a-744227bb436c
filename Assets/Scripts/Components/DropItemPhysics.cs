using System.Collections;
using UnityEngine;

public class DropItemPhysics : BaseDropItem
{
    [Header("贝塞尔曲线参数")]
    [SerializeField] private float animationDuration = 0.6f; // 动画持续时间
    [SerializeField] private float maxHeight = 2f; // 最大高度
    [SerializeField] private float maxDistance = 2f; // 最大水平距离
    [SerializeField] private float endYOffset = 0f; // 结束位置Y轴偏移（相对于地面）
    [SerializeField] private AnimationCurve heightCurve; // 高度曲线
    [SerializeField] private int bounceCount = 1; // 弹跳次数
    [SerializeField] private float bounceHeightRatio = 0.3f; // 弹跳高度比例（相对于主抛物线）
    [SerializeField] private float bounceDurationRatio = 0.4f; // 弹跳持续时间比例

    private Vector3 startPosition;
    private Vector3 targetPosition;

    private void Start()
    {
        // 初始化默认高度曲线（如果未设置）
        if (heightCurve.length == 0)
        {
            heightCurve = new AnimationCurve(
                new Keyframe(0, 0, 2, 2),
                new Keyframe(0.5f, 1, 0, 0),
                new Keyframe(1, 0, -2, -2)
            );
        }

        // 保存起始位置
        startPosition = transform.position;
        
        // 计算随机目标位置
        float randomAngle = Random.Range(0, 360) * Mathf.Deg2Rad;
        float randomDistance = Random.Range(0.5f, maxDistance);
        Vector3 horizontalOffset = new Vector3(
            Mathf.Cos(randomAngle) * randomDistance,
            0,
            0
        );
        
        targetPosition = startPosition + horizontalOffset;
        targetPosition.y += endYOffset; // 应用Y轴偏移
        
        // 开始动画
        StartCoroutine(AnimateDrop());
    }

    private IEnumerator AnimateDrop()
    {
        // 主抛物线动画
        float elapsedTime = 0;
        while (elapsedTime < animationDuration)
        {
            float t = elapsedTime / animationDuration;
            
            // 计算水平位置（线性插值）
            Vector3 horizontalPosition = Vector3.Lerp(startPosition, targetPosition, t);
            
            // 计算垂直位置（使用高度曲线）
            float heightFactor = heightCurve.Evaluate(t);
            float height = maxHeight * heightFactor;
            
            // 设置物体位置
            transform.position = new Vector3(
                horizontalPosition.x,
                horizontalPosition.y + height,
                horizontalPosition.z
            );
            
            elapsedTime += Time.deltaTime;
            yield return null;
        }
        
        // 确保到达目标位置
        transform.position = targetPosition;
        
        // 执行弹跳动画
        for (int i = 0; i < bounceCount; i++)
        {
            yield return StartCoroutine(AnimateBounce(i));
        }
    }
    
    private IEnumerator AnimateBounce(int bounceIndex)
    {
        float bounceDuration = animationDuration * bounceDurationRatio;
        float bounceHeight = maxHeight * bounceHeightRatio * (1f / (bounceIndex + 1));
        
        if (bounceHeight < 0.05f) // 如果弹跳高度太小，就不执行
            yield break;
            
        Vector3 bounceStart = transform.position;
        
        float elapsedTime = 0;
        while (elapsedTime < bounceDuration)
        {
            float t = elapsedTime / bounceDuration;
            
            // 使用抛物线曲线计算弹跳高度
            float heightFactor = Mathf.Sin(t * Mathf.PI);
            float height = bounceHeight * heightFactor;
            
            // 设置物体位置
            transform.position = new Vector3(
                bounceStart.x,
                targetPosition.y + height,
                bounceStart.z
            );
            
            elapsedTime += Time.deltaTime;
            yield return null;
        }
        
        // 确保回到地面位置
        transform.position = new Vector3(
            bounceStart.x,
            targetPosition.y,
            bounceStart.z
        );
    }
    
    // 可以添加一个方法来设置目标位置
    public void SetTargetPosition(Vector3 position)
    {
        targetPosition = position;
    }
    
    // 可以添加一个方法来设置最大高度
    public void SetMaxHeight(float height)
    {
        maxHeight = height;
    }
}