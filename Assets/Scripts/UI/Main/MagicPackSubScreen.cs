using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using System;
using MoreMountains.Tools;
using UnityEngine.UI;

public class MagicPackSubScreen : SubScreenBase, MMEventListener<OwnedMagicChangedEvent>
{
    MagicPackSubCtrl mCtrl; 
    public List<GameObject> m_ListMagic = new List<GameObject>(); // 法术
    public List<GameObject> m_ListSlots = new List<GameObject>(); // 槽位
    public List<GameObject> MagicPackCurSlots
    {
        get => m_ListSlots;
    }

    public MagicPackSubScreen(MagicPackSubCtrl subCtrl) : base(subCtrl)
    {
    }

    protected override void Init()
    {
        base.Init();
        mCtrl = mCtrlBase as MagicPackSubCtrl;

        mCtrl.MagicTransform.gameObject.SetActive(false);
        for (int i = 0; i < mCtrl.HoleList.Count; i++)
        {
            if (i >= m_ListMagic.Count)
            {
                GameObject newObject = UnityEngine.Object.Instantiate(mCtrl.MagicTransform.gameObject, mCtrl.transform);
                m_ListMagic.Add(newObject);
                m_ListMagic[i].GetComponent<RectTransform>().anchoredPosition = mCtrl.HoleList[i].gameObject.GetComponent<RectTransform>().anchoredPosition;
                m_ListMagic[i].gameObject.SetActive(false);
            }
        }

        m_ListSlots.Clear();
        for (int i = 0; i < mCtrl.HoleList.Count; i++)
        {
            m_ListSlots.Add(mCtrl.HoleList[i].gameObject);
        }

        this.MMEventStartListening<OwnedMagicChangedEvent>();
    }

    public override void Dispose()
    {
        base.Dispose();
        this.MMEventStopListening<OwnedMagicChangedEvent>();
    }

    public void OnMMEvent(OwnedMagicChangedEvent eventType)
    {
        if (eventType.EventName == "MagicPackChanged")
        {
            foreach (var item in m_ListMagic)
            {
                item.gameObject.SetActive(false);
                item.GetComponent<MagicSkillItemSubCtrl>().SlotElement = null;
            }

            List<SlotElement> allElements = eventType.ListParameter as List<SlotElement>;
            foreach (var e in allElements)
            {
                if (e.SlotType == 2) // 若在槽位中
                {
                    if (e.SlotIndex == -1)
                    { // 此Element还没有被设置位置, 默认找一个空位置
                        int tempCanSetIndex = -1;
                        for (int i = 0; i < m_ListMagic.Count; i++)
                        {
                            if (!m_ListMagic[i].gameObject.activeSelf)
                            {
                                tempCanSetIndex = i;
                                break;
                            }
                        }

                        e.SlotIndex = tempCanSetIndex;
                    }

                    m_ListMagic[e.SlotIndex].gameObject.SetActive(true);
                    string loadMagicPath = string.Empty;
                    if (e.Type == ElementType.Buff)
                    {
                        loadMagicPath = string.Format("BuffItem/{0}", e.ID);
                    }else if (e.Type == ElementType.Skill)
                    {
                        loadMagicPath = string.Format("SkillItem/{0}", e.ID);
                    }

                    Sprite newIconSprite = Resources.Load<Sprite>(loadMagicPath);
                    if (newIconSprite != null)
                    {
                        m_ListMagic[e.SlotIndex].GetComponent<MagicSkillItemSubCtrl>().Icon.sprite = newIconSprite;
                    }

                    m_ListMagic[e.SlotIndex].GetComponent<MagicSkillItemSubCtrl>().SlotElement = e;
                }
            }
        }
    }
}