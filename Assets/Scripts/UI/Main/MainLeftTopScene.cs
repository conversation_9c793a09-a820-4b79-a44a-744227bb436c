using MoreMountains.Tools;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class MainLeftTopSceneParam : UIOpenScreenParameterBase
{

}


public class MainLeftTopScene : ScreenBase, MMEventListener<CoinChangedEvent>
{
    PlayAvatarInfoCtrl mCtrl;
    HealthSubScreen healthSubScreen;
    MagicSubScreen magicSubScreen;


    public MainLeftTopScene(UIOpenScreenParameterBase param = null) : base(UIConst.UIMainLeftTop, param)
    {

    }

    public override void OnClose()
    {
        base.OnClose();
        this.MMEventStopListening<CoinChangedEvent>();
    }

    protected override void OnLoadSuccess()
    {
        base.OnLoadSuccess();
        mCtrl = mCtrlBase as PlayAvatarInfoCtrl;

        healthSubScreen = new HealthSubScreen(mCtrl.m_HealthSubCtrl);
        magicSubScreen = new MagicSubScreen(mCtrl.m_MagicSubCtrl);

        this.MMEventStartListening<CoinChangedEvent>();

    }

    protected override void UIAdapt(Vector2Int res)
    {
        // Debug.Log(string.Format("�ֱ��ʷ����˱仯����Ϊ{0},��Ϊ{1}", res.x, res.y));
    }

    public void setPlayerID(string playerID)
    {
        PlayerData.GetInstance().PlayerID = playerID;
    }

    public void UpdateHealth(float currentHealth, float minHealth, float maxHealth)
    {
        healthSubScreen.UpdateHealth(currentHealth, minHealth, maxHealth);
    }

    public void UpdateMagic(float currentMagic, float minMagic, float maxMagic)
    {
        magicSubScreen.UpdateMagic(currentMagic, minMagic, maxMagic);
    }

    public void UpdateCoin(int coin)
    {
        mCtrl.coin.text = string.Format("x{0}", coin.ToString());
    }

    public void OnMMEvent(CoinChangedEvent eventType)
    {
        if (eventType.EventName == "CoinChanged")
        {
            this.UpdateCoin(eventType.IntParameter);
        }
    }
}
