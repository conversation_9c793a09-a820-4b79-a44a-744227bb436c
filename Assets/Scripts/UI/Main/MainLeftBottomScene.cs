using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class MainLeftBottomSceneParam : UIOpenScreenParameterBase
{

}


public class MainLeftBottomScene : ScreenBase
{
    MainLeftBottomCtrl mCtrl;
    StaffSubScreen staffSubScreen;
    MagictomSubScreen magictomSubScreen;

    public MainLeftBottomScene(UIOpenScreenParameterBase param = null) : base(UIConst.UIMainLeftBottom, param)
    {

    }

    protected override void OnLoadSuccess()
    {
        base.OnLoadSuccess();
        mCtrl = mCtrlBase as MainLeftBottomCtrl;

        staffSubScreen = new StaffSubScreen(mCtrl.m_StaffSubCtrl);
        magictomSubScreen = new MagictomSubScreen(mCtrl.m_MagictomSubCtrl);

    }

    protected override void UIAdapt(Vector2Int res)
    {
        // Debug.Log(string.Format("�ֱ��ʷ����˱仯����Ϊ{0},��Ϊ{1}", res.x, res.y));
    }

    public void UpdateWeaponItem(MagicStaff magicStaff)
    {
        staffSubScreen.UpdateWeaponItem(magicStaff);
    }

    public void UpdateStaffSockets(MagicStaff magicStaff)
    {
        staffSubScreen.UpdateStaffSockets(magicStaff);
    }

    public void UpdateMagicSkills(List<SlotElement> slotElements)
    {
        staffSubScreen.UpdateMagicSkills(slotElements);
    }

    public List<GameObject> GetCurMagicStaffSlots()
    {
        return staffSubScreen.GetCurMagicStaffSlots();
    }

    public List<GameObject> GetMagicPackCurSlots()
    {
        return magictomSubScreen.GetMagicPackCurSlots();
    }

}
