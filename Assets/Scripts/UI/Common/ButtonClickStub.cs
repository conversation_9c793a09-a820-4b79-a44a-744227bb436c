using UnityEngine;

public class ButtonClickStub : MonoBehaviour
{
    public void OnClickOpenBuffTips()
    {
        var scene = GameUIManager.GetInstance().GetUI(typeof(StaffTipsScene));
        if (scene != null && scene.IsShow())
        {
            GameUIManager.GetInstance().CloseUI(typeof(StaffTipsScene));
        }

        var stlotElement = transform.GetComponent<MagicSkillItemSubCtrl>().SlotElement;
        GameUIManager.GetInstance().OpenUI(typeof(BuffTipsScene), new BuffTipsSceneParam(stlotElement));

    }

    public void OnClickCloseStaffTips()
    {
        GameUIManager.GetInstance().CloseUI(typeof(BuffTipsScene));
    }
}