
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.EventSystems;
 
public class ButtonHandler : <PERSON>oB<PERSON><PERSON>our, IPointerDownHandler, IPointerUpHandler
{
    public void OnPointerDown(PointerEventData eventData)
    {
        transform.localScale = Vector3.one * 1.2f;
        transform.localScale = Vector3.one * 0.8f;
    }
 
    public void OnPointerUp(PointerEventData eventData)
    {
        transform.localScale = Vector3.one;
    }

}