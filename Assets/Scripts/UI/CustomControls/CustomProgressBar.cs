using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using System;

public class CustomProgressBar : MonoBehaviour
{
    public Image progressBarFill;
    public Image progressBarBuoy;
    
    // ����¼������������ﵽ100%ʱ����
    public event Action OnProgressComplete;
    
    // ���һ����־�����ڸ��ٽ������Ƿ������
    private bool _isComplete = false;

    void Update()
    {
        if (progressBarFill != null && progressBarBuoy != null)
        {
            float progressBarWidth = progressBarFill.rectTransform.rect.width;
            float buoyPositionX = progressBarWidth * progressBarFill.fillAmount;
            progressBarBuoy.rectTransform.anchoredPosition = new Vector2(buoyPositionX, progressBarBuoy.rectTransform.anchoredPosition.y);
        }
    }

    public void SetProgress(float progress)
    {
        if (progress < 0 || progress > 1)
        {
            Debug.LogError("Progress value must be between 0 and 1.");
            return;
        }

        if (progressBarFill != null)
        {
            progressBarFill.fillAmount = progress;
            
            // �������Ƿ�ﵽ1.0��100%��
            if (progress >= 1.0f && !_isComplete)
            {
                _isComplete = true;
                progressBarBuoy.gameObject.SetActive(false);
                // ��������¼�
                OnProgressComplete?.Invoke();
            }
            else if (progress < 1.0f)
            {
                if (!progressBarBuoy.gameObject.activeSelf)
                {
                    progressBarBuoy.gameObject.SetActive(true);
                }
                _isComplete = false;
            }
        }
    }
    
    // ���һ�����������������Ƿ������
    public bool IsComplete()
    {
        return _isComplete;
    }
}
