using MoreMountains.Tools;
using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using static System.Collections.Specialized.BitVector32;

public class GameLobby : MonoBehaviour
{
    public Button startGameBtn;
    // Start is called before the first frame update
    void Start()
    {
        startGameBtn.onClick.AddListener(StartGame);
    }

    private void StartGame()
    {
        if (PlayerData.GetInstance().CurSection == 0 || PlayerData.GetInstance().CurLevel == 0)
        {// �״ν�����Ϸ
            var (nextSection, nextLevel) = Formula.GetNextSection();
            PlayerData.GetInstance().CurSection = nextSection;
            PlayerData.GetInstance().CurLevel = nextLevel;

            cfg.section.SectionConfiguration curScf = DataTableManager.Instance.Tables.TbSection.Get(section: PlayerData.GetInstance().CurSection, level: PlayerData.GetInstance().CurLevel);
            List<int> bonusesModeList = Formula.GetRandomElementsBasedOnWeights(curScf.RandomBonusMode, count: 1);
            PlayerData.GetInstance().LevelData.RandomBonusModeList = bonusesModeList;
            if(bonusesModeList.Count == 1)
            {
                PlayerData.GetInstance().LevelData.BonusMode = bonusesModeList[0];
            }
        }

        int section = PlayerData.GetInstance().CurSection;
        int level = PlayerData.GetInstance().CurLevel;
        cfg.section.SectionConfiguration scf = DataTableManager.Instance.Tables.TbSection.Get(section: section, level: level);
        int mapID = MMSceneLoadingManager.GetRandomScene(scf.RandomMapLibrary);
        PlayerData.GetInstance().LevelData.MapID = mapID;
        cfg.map.MapConfiguration mapCfg = DataTableManager.Instance.Tables.TbMap.Get(mapID);
        MMSceneLoadingManager.LoadScene(mapCfg.ResourceName);
    }

    // Update is called once per frame
    void Update()
    {
        
    }
}
