using MoreMountains.TopDownEngine;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class StartGameUI : MonoBehaviour
{
    private void Awake()
    {
        GameUIManager.GetInstance().OpenUI(typeof(MainLeftTopScene));
        GameUIManager.GetInstance().OpenUI(typeof(MainLeftBottomScene));
        GameUIManager.GetInstance().OpenUI(typeof(MainJoystickScreen));
    }
    // Start is called before the first frame update
    void Start()
    {
    }

    void OnGUI()
    {
        GUILayout.BeginArea(new Rect(10, 10, 300, 200));
        GUILayout.Label("Debugger");
        if (GUILayout.Button("���һ���������"))
        {
            int index = Random.Range(0, 2);
            if (index == 0)
            {   // �������ǿ��
                int spellID = Random.Range(100001, 100003);
                cfg.skill.Buff buff = DataTableManager.Instance.Tables.TbBuff.Get(spellID);
                if (buff.Id == 100001)
                {
                    var numBuff = BuffFactory.CreateNumBuff(buff);
                    numBuff.SlotType = 2;
                    // Debug.Log("���: ������ǿ��: �޸���Ŀ��");
                    PlayerData.GetInstance().AddSlotElement(numBuff);
                }
                else if (buff.Id == 100002)
                {
                    var sizeBuff = BuffFactory.CreateSizeBuff(buff);
                    sizeBuff.SlotType = 2;
                    // Debug.Log("���: ������ǿ��: �޸Ĵ�С��");
                    PlayerData.GetInstance().AddSlotElement(sizeBuff);
                }
            }
            else if (index == 1)
            {
                // Debug.Log("���: ������: �ɵ���");
                var skillProjectile = new SkillProjectile(10001, "����: �ɵ�");
                skillProjectile.SlotType = 2;
                PlayerData.GetInstance().AddSlotElement(skillProjectile);
            }
        }else if(GUILayout.Button("���һ������"))
        {
            int index = Random.Range(1001, 1003);
            Debug.Log(string.Format("�������: {0}", index));
            PlayerData.GetInstance().MagicStaffID = (uint)index;
        }



        GUILayout.EndArea();
    }

}
