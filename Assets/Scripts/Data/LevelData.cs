using System.Collections.Generic;

public class LevelData
{
    public int m_MapID = 0; // ��ͼID
    public int m_CurMonsterRefreshIndex = 1; // ��ǰ����ˢ�²���
    public int m_BonusMode = 0; // ����ģʽ
    public List<int> m_RandomBonusModeList = new List<int>(); // ��ǰ������Ľ���ģʽ

    public int MapID
    {
        get { return m_MapID; }
        set { m_MapID = value; }
    }

    public int CurMonsterRefreshIndex
    {
        get { return m_CurMonsterRefreshIndex; }
        set { m_CurMonsterRefreshIndex = value; }
    }

    public List<int> RandomBonusModeList
    {
        get { return m_RandomBonusModeList; }
        set { m_RandomBonusModeList = value; }
    }

    public int BonusMode
    {
        get { return m_BonusMode; }
        set { m_BonusMode = value; }
    }
}