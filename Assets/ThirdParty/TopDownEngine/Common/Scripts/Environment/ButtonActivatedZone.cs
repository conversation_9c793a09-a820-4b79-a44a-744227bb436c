using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using MoreMountains.Tools;
using UnityEngine.Events;

namespace MoreMountains.TopDownEngine
{
	/// <summary>
	/// Add this component to a collider 2D and you'll be able to have it perform an action on enter/exit and while staying
	/// </summary>
	[AddComponentMenu("TopDown Engine/Environment/Button Activated Zone")]
	public class ButtonActivatedZone : ButtonActivated
	{

	}
}