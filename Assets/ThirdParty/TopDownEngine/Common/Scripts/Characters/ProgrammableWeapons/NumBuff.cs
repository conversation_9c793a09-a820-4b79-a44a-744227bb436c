using UnityEngine;

public class NumBuff : BuffElement
{
    public int Num = 1;

    public NumBuff() { }

    public NumBuff(uint id, string elementName = "")
    {
        ID = id;
        Type = ElementType.Buff;
        EffectivePoint = BuffEffectivePoint.CASTING;
        ElementName = elementName;
    }

    public override void ApplyBuff(SkillElement target)
    {
        if (target != null)
        {
            var targetType = target.GetType();
            var numberField = targetType.GetField("number");
            if (numberField != null && numberField.FieldType == typeof(int))
            {
                numberField.SetValue(target, Num);
            }
        }
    }
}