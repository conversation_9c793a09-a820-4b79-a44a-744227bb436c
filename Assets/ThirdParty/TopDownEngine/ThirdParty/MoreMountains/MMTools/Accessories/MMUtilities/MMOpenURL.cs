using UnityEngine;

namespace MoreMountains.Tools
{
	/// <summary>
	/// A class used to open a URL specified in its inspector
	/// </summary>
	[AddComponentMenu("More Mountains/Tools/Utilities/MM Open URL")]
	public class MMOpenURL : <PERSON>o<PERSON><PERSON><PERSON><PERSON> 
	{
		/// the URL to open when calling OpenURL()
		public string DestinationURL;

		/// <summary>
		/// Opens the URL specified in the DestinationURL field
		/// </summary>
		public virtual void OpenURL()
		{
			Application.OpenURL(DestinationURL);
		}		
	}
}