Shader "MoreMountains/ConeOfLight"
{
    Properties
    {
        _MainTex("Diffuse Texture", 2D) = "white" {}
        _VisionMask("Vision Mask", 2D) = "white" {}
        _Contrast("Contrast", Float) = 0.5
        _Color("Color", Color) = (1,1,1,1)
        _MaskIntensity("Mask Intensity", Range(0, 1)) = 1
        _VisionPosition("Vision Position", Vector) = (0,0,0,0)
        _VisionAngle("Vision Angle", Float) = 0
        _VisionRadius("Vision Radius", Float) = 5.0
    }

    SubShader
    {
        Tags
        {
            "ForceNoShadowCasting" = "True"
            "Queue" = "Transparent"
            "RenderType" = "Transparent"
            "IgnoreProjector" = "True"
        }

        Pass
        {
            ZTest Always
            AlphaTest Greater 0.0
            Blend DstColor One
            Cull Off

            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #include "UnityCG.cginc"

            uniform sampler2D _MainTex;
            uniform sampler2D _VisionMask;
            uniform float _Contrast;
            uniform float4 _Color;
            uniform float _MaskIntensity;
            uniform float4 _VisionPosition;
            uniform float _VisionAngle;
            uniform float _VisionRadius;

            struct VertexInput
            {
                float4 vertex : POSITION;
                float4 uv : TEXCOORD0;
                float4 color : COLOR;
            };

            struct VertexOutput
            {
                float4 pos : SV_POSITION;
                float2 uv : TEXCOORD0;
                float4 color : COLOR;
                float4 worldPos : TEXCOORD1;
            };

            VertexOutput vert(VertexInput input)
            {
                VertexOutput output;
                output.pos = UnityObjectToClipPos(input.vertex);
                output.uv = input.uv;
                output.color = input.color;
                output.worldPos = mul(unity_ObjectToWorld, input.vertex);
                return output;
            }

            float2 RotateUV(float2 uv, float angle)
            {
                float s = sin(angle);
                float c = cos(angle);
                float2x2 rotationMatrix = float2x2(c, -s, s, c);
                return mul(rotationMatrix, uv);
            }

            float4 frag(VertexOutput input) : COLOR
            {
                float4 diffuse = tex2D(_MainTex, input.uv);
                
                float2 toFragment = input.worldPos.xy - _VisionPosition.xy;
                
                float2 rotatedPos = RotateUV(toFragment, radians(-_VisionAngle));
                
                float2 maskUV = rotatedPos / (_VisionRadius * 2) + 0.5;
                
                float4 mask = tex2D(_VisionMask, maskUV);
                
                float distanceFromCenter = length(toFragment);
                float inRange = distanceFromCenter <= _VisionRadius ? 1 : 0;
                
                diffuse.rgb = diffuse.rgb * _Color.rgb * input.color.rgb;
                diffuse.rgb *= diffuse.a * _Color.a * input.color.a;
                diffuse *= _Contrast;
                
                diffuse.rgb *= lerp(1, mask.r * inRange, _MaskIntensity);
                
                return float4(diffuse);
            }
            ENDCG
        }
    }
}