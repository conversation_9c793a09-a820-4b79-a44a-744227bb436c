using cfg.skill;
using MoreMountains.TopDownEngine;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.EventSystems;

public class FloatingJoystick : Joystick
{
    // 缓存InputManager引用
    private InputManager _inputManager;
    
    protected override void Start()
    {
        base.Start();
        background.gameObject.SetActive(false);
        
        // 缓存InputManager引用
        _inputManager = SIXGameManager.Instance.GetComponent<InputManager>();
    }

    public void Update()
    {
        // 使用缓存的引用而不是每帧GetComponent
        if (_inputManager != null)
        {
            _inputManager.SetMovement(Direction);
        }
    }

    public override void OnPointerDown(PointerEventData eventData)
    {
        background.anchoredPosition = ScreenPointToAnchoredPosition(eventData.position);
        background.gameObject.SetActive(true);
        base.OnPointerDown(eventData);
    }

    public override void OnPointerUp(PointerEventData eventData)
    {
        background.gameObject.SetActive(false);
        base.OnPointerUp(eventData);
    }
}